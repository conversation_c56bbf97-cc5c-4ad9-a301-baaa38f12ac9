CREATE OR REPLACE FUNCTION public.tms_get_site_attendance_dump_fr_vertical(vertical_id_ integer, materialized_table_name_ text, assgn_to_prvdr_start_date_ timestamp, assgn_to_prvdr_end_date_ timestamp)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	_dynamic_sql text;

	begin

		-- Create materialized view for site attendance dump
		_dynamic_sql = 'CREATE MATERIALIZED VIEW '||materialized_table_name_||' AS
								select
									vertical_settings.settings_data->>$$vertical_title$$ as "Vertical Name",
								   srvc_req.display_code as "Service Display Code",
								   org.nickname as "Org Name",
								   srvc_type.title as "Service Type"
							      from public.cl_tx_srvc_req as srvc_req
							     inner join cl_tx_orgs_settings as vertical_settings
							        on vertical_settings.db_id = ' || vertical_id_ || '
							     inner join cl_tx_orgs as org
							        on org.org_id = srvc_req.org_id
							     inner join cl_cf_service_types as srvc_type
							        on srvc_type.service_type_id = srvc_req.srvc_type_id
								 where srvc_req.prvdr_vertical = ' || vertical_id_ || '
								   and srvc_req.srvc_prvdr_assg_time >= $$' || assgn_to_prvdr_start_date_ || '$$
								   and srvc_req.srvc_prvdr_assg_time <= $$' || assgn_to_prvdr_end_date_ || '$$
								 order by srvc_req.db_id desc
					';

		raise notice 'site attendance dump sql %', _dynamic_sql;

		execute _dynamic_sql;
		return '{}';

	END;
$function$
;
