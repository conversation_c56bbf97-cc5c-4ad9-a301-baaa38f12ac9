const sampleOperationResp = require('./utils/operationResp');
const HttpStatus = require('http-status-codes');
const {
    internalProcessJobTypes,
} = require('./queues_v2/processors/process_internal_operations');
const users_model = require('./users_model');
const { allQueues } = require('./queues_v2/queues');
let db_resp = require('./utils/db_resp');
const JSONStream = require('JSONStream');
const jsonToCsv = require('json-to-csv-stream');
const path = require('path');
const fs = require('fs');
const {
    jiffyDailyUsageReportTemplate,
} = require('./queues_v2/processors/email/templates/jiffy_daily_usage_report_template');
class internal_operations_model {
    refreshGeoStatus(query) {
        return new Promise(async (resolve, reject) => {
            // console.log('yeti query initial', query);
            let data = {
                org_id: query.org_id,
                from_date: query.from_date,
                to_date: query.to_date,
                current_geo_status: query.current_geo_status,
            };
            try {
                allQueues.WIFY_INTERAL_OPERATIONS.addJob({
                    jobType: internalProcessJobTypes.geoStatusRefresh,
                    data,
                });

                resolve(
                    new sampleOperationResp(
                        true,
                        'Refresh geo status API triggered',
                        HttpStatus.StatusCodes.OK
                    )
                );

                return;
            } catch (error) {
                console.log(
                    'internal_model :: refreshGeoStatus :: error :: ',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Something went wrong',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
        });
    }

    getJiffyDailyUsage(query) {
        return new Promise(async (resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(query || {});
                let yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1); // move 1 day back
                yesterday = yesterday.toISOString().slice(0, 10); // format YYYY-MM-DD

                let filters_ = {
                    date_range: [yesterday, yesterday],
                };
                const dbObj = this.dbReplica || this.db;
                if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                console.log(
                    'getJiffyDailyUsage :: requesterInfo',
                    requesterInfo
                );
                console.log(
                    'getJiffyDailyUsage :: filters_',
                    JSON.stringify(filters_)
                );
                dbObj
                    .tms_get_jiffy_usage_daily(
                        requesterInfo,
                        JSON.stringify(filters_)
                    )
                    .then(
                        (res) => {
                            console.log('res', res);

                            var dbResp = new db_resp(
                                res[0].tms_get_jiffy_usage_daily
                            );

                            if (!dbResp.status) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Internal server Error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );

                                return;
                            }

                            const emailNotificationDetails = {
                                data: dbResp.data,
                            };
                            let message = jiffyDailyUsageReportTemplate(
                                emailNotificationDetails
                            );
                            const usr_id = query?.usr_id;
                            const ip_address = query?.ip_addr;
                            const user_agent = query?.user_agent;
                            const org_id = query?.org_id;
                            const to =
                                '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>';
                            const subject = 'Jiffy Daily Usage Report';
                            const attachments = [];
                            const emailJobData = {
                                to,
                                subject,
                                message,
                                attachments,
                                org_id,
                                usr_id,
                                ip_address,
                                user_agent,
                            };
                            // console.log("emailJobData",emailJobData)
                            allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
                            console.log(
                                'Added to email queue for getJiffyDailyUsage'
                            );
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    'Added to email queue',
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                            return;
                        },
                        (err) => {
                            console.log(
                                'getJiffyDailyUsage :: EXPORT_DUMP_ERR ......',
                                err
                            );
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                console.log(
                    'getJiffyDailyUsage :: EXPORT_DUMP_ERR ......',
                    error
                );
                this.fatalDbError(resolve, error);
            }
        });
    }
    deleteExternalApiLogs(requestBody) {
        return new Promise(async (resolve, reject) => {
            try {
                // Validate required fields
                if (!requestBody.deletion_till) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'deletion_till is required in request body',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                    return;
                }

                let data = {
                    deletion_till: requestBody.deletion_till,
                    limit: requestBody.limit || 100000, // Default to 100k if not provided
                };

                allQueues.WIFY_INTERAL_OPERATIONS.addJob({
                    jobType: internalProcessJobTypes.deleteApiLogs,
                    data,
                });

                resolve(
                    new sampleOperationResp(
                        true,
                        `Delete external API logs triggered for date: ${data.deletion_till} with limit: ${data.limit}`,
                        HttpStatus.StatusCodes.OK
                    )
                );

                return;
            } catch (error) {
                console.log(
                    'internal_model :: deleteExternalApiLogs :: error :: ',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Something went wrong',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
        });
    }

    processApiLogsDeletion(query) {
        return new Promise(async (resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            const form_data = JSON.stringify(query.data);
            console.log(
                'processApiLogsDeletion :: Starting deletion process with data:',
                form_data
            );

            let totalDeletedCount = 0;
            let batchNumber = 1;
            let hasMoreData = true;

            try {
                // Process ALL batches in this single queue job
                while (hasMoreData) {
                    // Use await to properly wait for each batch
                    const res =
                        await this.db.tms_delete_old_external_api_logs(
                            form_data
                        );

                    const dbResp = new db_resp(
                        res[0].tms_delete_old_external_api_logs
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    const batchDeletedCount = dbResp.data?.deleted_count || 0;
                    const batchLimit = query.data.limit || 100000;
                    hasMoreData = dbResp.data?.has_more_data || false;
                    totalDeletedCount += batchDeletedCount;

                    console.log(
                        `processApiLogsDeletion :: Batch ${batchNumber} - Deleted ${batchDeletedCount} records. Total deleted: ${totalDeletedCount}. Has more data: ${hasMoreData}`
                    );

                    // Stop if no records were deleted OR if deleted count is less than limit
                    // (indicating we've reached the end of available records)
                    if (
                        batchDeletedCount === 0 ||
                        batchDeletedCount < batchLimit
                    ) {
                        hasMoreData = false;
                    }

                    batchNumber++;
                }

                console.log(
                    `processApiLogsDeletion :: Deletion completed. Total deleted: ${totalDeletedCount} records across ${batchNumber - 1} batches`
                );

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify({
                            total_deleted_count: totalDeletedCount,
                            total_batches: batchNumber - 1,
                            message: `Deletion completed. Deleted ${totalDeletedCount} records in ${batchNumber - 1} batches`,
                        }),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.log(
                    'processApiLogsDeletion :: Error during processing:',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Database error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    updateGeoStatus(query) {
        return new Promise(async (resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            // console.log('Form data to function', query);
            const form_data = JSON.stringify(query.data);
            this.db.tms_refresh_geo_verification_status(form_data).then(
                (res) => {
                    const dbResp = new db_resp(
                        res[0].tms_refresh_geo_verification_status
                    );
                    console.log(
                        'internal_model :: updateGeoStatus :: dbResp after refresh ::',
                        dbResp
                    );
                    if (dbResp.code == 'No tasks found') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'No subtasks with defined geo state found.',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    console.log('error hu mai', error);
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new internal_operations_model();
        return instance;
    }

    getInternalOperationsModelData(
        internal_operations_model,
        getUserAndOrgIdFrQuery = false
    ) {
        let userAndOrgIdFrQuery = {};
        // if (getUserAndOrgIdFrQuery) {
        //     userAndOrgIdFrQuery = {
        //         org_id: users_model.getOrgId(this.userContext),
        //         usr_id: users_model.getUUID(this.userContext),
        //     };
        // }
        return {
            ip_address: internal_operations_model.ip_address,
            user_agent: internal_operations_model.user_agent_,
            user_context: internal_operations_model.user_context,
            ...userAndOrgIdFrQuery,
        };
    }
}

module.exports = new internal_operations_model();
