CREATE OR REPLACE FUNCTION public.tms_get_site_attendance_dump_fr_vertical(vertical_id_ integer, materialized_table_name_ text, assgn_to_prvdr_start_date_ timestamp, assgn_to_prvdr_end_date_ timestamp)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
	_dynamic_sql text;

	begin

		-- Create materialized view for site attendance dump
		_dynamic_sql = 'CREATE MATERIALIZED VIEW '||materialized_table_name_||' AS
								select
									srvc_req.display_code as "TMS ID",
									srvc_status.title as "Status",
									srvc_req.c_meta.time as "C_DATE",
									c_by_usr.name as "Added by",
									u_by_usr.name as "Last Updated By",
									srvc_req.u_meta.time::date as "Last Updated Date",
									srvc_req.u_meta.time::time as "Last Updated Time",
									srvc_req.form_data->>$$cust_mobile$$ as "Mobile(+91)",
									srvc_req.form_data->>$$cust_name$$ as "Name",
									srvc_req.form_data->>$$cust_email$$ as "Email",
									srvc_req.form_data->>$$description$$ as "Description",
									srvc_req.form_data->>$$req_srvc_date$$ as "Req. Service Date",
									srvc_req.priority as "Priority",
									srvc_req.form_data->>$$labels$$ as "Labels",
									srvc_req.form_data->>$$cc_users$$ as "CC users",
									srvc_req.c_meta.time::date as "Creation Date",
									vertical_settings.settings_data->>$$vertical_title$$ as "Request vertical",
									srvc_req.form_data->>$$cust_line_0$$ as "Flat no",
									srvc_req.form_data->>$$cust_line_1$$ as "Building/Apartment name",
									srvc_req.form_data->>$$cust_line_2$$ as "Line 1",
									srvc_req.form_data->>$$cust_line_3$$ as "Line 2",
									srvc_req.form_data->>$$cust_pincode$$ as "Pincode",
									srvc_req.form_data->>$$cust_city$$ as "City",
									srvc_req.form_data->>$$cust_state$$ as "State",
									srvc_req.db_id as "Request ID",
									tech_usr.name as "Technician name",
									tech_usr.form_data->>$$employee_code$$ as "Employee Code",
									tech_usr.form_data->>$$role$$ as "Role",
									sbtsk.form_data->>$$sbtsk_start_day$$ as "Day",
									sbtsk.form_data->>$$time_slot$$ as "Time slot",
									sbtsk_type.title as "Task type",
									sbtsk_status.title as "Task status",
									'''' as "Attendance",
									sbtsk.start_time::date as "Start Date",
									sbtsk.start_time::time as "Start time",
									sbtsk.end_time::time as "End time",
									CASE
										WHEN (sbtsk.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$) ~ $$^[0-9]*\.?[0-9]+$$$
											THEN (sbtsk.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$)::float
										ELSE NULL
									END as "GAI Info",
									CASE
										WHEN (sbtsk.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$) ~ $$^[0-9]*\.?[0-9]+$$$
											THEN (sbtsk.form_data->$$gai_rating$$->$$gemini$$->>$$rating$$)::float
										ELSE NULL
									END as "GAI rating",
									COALESCE(sbtsk.form_data->$$gai_rating$$->$$gemini$$->>$$description$$, $$NA$$) as "GAI remarks"
							      from public.cl_tx_srvc_req as srvc_req
							     inner join cl_tx_orgs_settings as vertical_settings
							        on vertical_settings.db_id = ' || vertical_id_ || '
							     inner join cl_tx_orgs as org
							        on org.org_id = srvc_req.org_id
							     inner join cl_cf_service_types as srvc_type
							        on srvc_type.service_type_id = srvc_req.srvc_type_id
							     inner join cl_cf_srvc_statuses as srvc_status
							        on srvc_status.srvc_id = srvc_req.srvc_type_id
							       and srvc_status.status_key = srvc_req.status
							     inner join cl_tx_users as c_by_usr
							        on c_by_usr.usr_id = srvc_req.c_by
							      left join cl_tx_users as u_by_usr
							        on u_by_usr.usr_id = srvc_req.u_by
							      left join cl_tx_sbtsk as sbtsk
							        on sbtsk.srvc_req_id = srvc_req.db_id
							       and sbtsk.is_deleted is not true
							      left join cl_cf_sbtsk_types as sbtsk_type
							        on sbtsk_type.sbtsk_type_id = sbtsk.sbtsk_type
							      left join cl_cf_sbtsk_statuses as sbtsk_status
							        on sbtsk_status.sbtsk_type_id = sbtsk.sbtsk_type
							       and sbtsk_status.status_key = sbtsk.status
							      left join cl_tx_users as tech_usr
							        on tech_usr.usr_id = ANY(sbtsk.assigned_to)
								 where srvc_req.prvdr_vertical = ' || vertical_id_ || '
								   and srvc_req.srvc_prvdr_assg_time >= $$' || assgn_to_prvdr_start_date_ || '$$
								   and srvc_req.srvc_prvdr_assg_time <= $$' || assgn_to_prvdr_end_date_ || '$$
								 order by srvc_req.db_id desc, sbtsk.db_id
					';

		raise notice 'site attendance dump sql %', _dynamic_sql;

		execute _dynamic_sql;
		return '{}';

	END;
$function$
;
