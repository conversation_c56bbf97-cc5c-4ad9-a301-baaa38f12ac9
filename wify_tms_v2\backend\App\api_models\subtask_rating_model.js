var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
const users_model = require('./users_model');
const { allQueues } = require('./queues_v2/queues');
const { s3 } = require('./utils/s3_helper');
const JSONStream = require('JSONStream');
const fs = require('fs');
const fsp = require('fs').promises;
const path = require('path');

function formatDate(date) {
    // Format dates as 'YYYY-MM-DD'
    return date.toISOString().split('T')[0];
}

function getStartAndEndDate() {
    const endDate = new Date();
    const startDate = new Date();

    startDate.setDate(endDate.getDate() - 2);

    return {
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
    };
}

function findIndexInArray(values, searchTerms) {
    for (let i = 0; i < values.length; i++) {
        if (searchTerms.some((term) => values[i].includes(term))) {
            return i;
        }
    }
    return -1;
}

class subtask_rating_model {
    rateAllSubtasks() {
        return new Promise(async (resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }

            try {
                const { startDate, endDate } = getStartAndEndDate();
                dbObj
                    .tms_get_subtasks_details('{}', { stream: true })
                    .then(async (stream) => {
                        const fileFolderPath = path.join(
                            'temp_files',
                            'subtask_details_dump'
                        );
                        try {
                            await fsp.mkdir(fileFolderPath, {
                                recursive: true,
                            });

                            const fileName = 'subtask_details.js';
                            const filePath = path.join(
                                fileFolderPath,
                                fileName
                            );

                            try {
                                await fsp.access(filePath);
                            } catch (error) {
                                console.log(
                                    'Rating file does not exist, creating file...'
                                );
                                await fsp.writeFile(filePath, '');
                            }

                            await fsp.truncate(filePath, 0);

                            const writeStream = fs.createWriteStream(filePath);

                            stream.on('data', (data) => {
                                console.log(
                                    'gai :: subtask_rating_model :: Streaming data ::',
                                    data
                                );
                                console.log(
                                    'gai :: subtask_rating_model :: Streaming data ::',
                                    JSON.stringify(
                                        data?.tms_get_subtasks_details?.data ||
                                            '{}'
                                    )
                                );
                                console.log(
                                    'gai :: subtask_rating_model :: Streaming data total count ::',
                                    data?.tms_get_subtasks_details?.data?.length
                                );
                                writeStream.write(
                                    JSON.stringify(
                                        data?.tms_get_subtasks_details?.data
                                    )
                                );
                            });

                            stream.on('end', () => {
                                writeStream.end();
                                console.log(
                                    'gai :: subtask_rating_model :: Streaming data :: upload to s3'
                                );
                                const s3Key = `2025_subtask_details_dump/subtask_details.js`; // S3 object key
                                const bucketName = process.env.S3_BUCKET; // S3 bucket from env

                                const uploadParams = {
                                    Bucket: bucketName,
                                    Key: s3Key,
                                    Body: fs.createReadStream(filePath),
                                    ContentType: 'application/javascript', // Optional: adjust MIME type if needed
                                };

                                s3.upload(uploadParams, (err, data) => {
                                    if (err) {
                                        console.error(
                                            'gai :: Failed to upload subtask details to S3:',
                                            err
                                        );
                                    } else {
                                        console.log(
                                            'gai :: File successfully uploaded to S3:',
                                            data.Location
                                        );
                                    }
                                });
                                // console.log('Streaming ended -----');
                                const readStream = fs
                                    .createReadStream(filePath)
                                    .pipe(JSONStream.parse('*'));

                                // const processedCount = 0;

                                readStream.on('data', (data) => {
                                    // console.log('gai :: subtask_rating_model :: Processing data ::', data);
                                    allQueues.WIFY_GAI_RATING_FOR_TECHNICIAN_SUBTASK.addJob(
                                        data
                                    );
                                    // processedCount++;
                                });

                                readStream.on('end', () => {
                                    console.log(
                                        `gai :: subtask_rating_model :: Completed processing subtasks records.`
                                    );
                                });

                                readStream.on('error', (err) => {
                                    console.error(
                                        'gai :: subtask_rating_model :: Error processing subtasks file:',
                                        err
                                    );
                                });
                            });

                            stream.on('error', (err) => {
                                console.log(
                                    'gai :: subtask_rating_model :: Stream encountered an error:',
                                    err
                                );
                                writeStream.end();
                            });

                            console.log(
                                'gai :: subtask_rating_model :: Streaming started for subtask details'
                            );
                        } catch (error) {
                            console.log(
                                'gai :: subtask_rating_model :: Error in ',
                                error
                            );
                            if (error.code !== 'ENOENT') {
                                console.log(
                                    'gai :: subtask_rating_model :: Error truncating file:',
                                    error
                                );
                            }

                            return resolve(
                                new sampleOperationResp(
                                    false,
                                    error?.message || 'Something went wrong',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                        }
                    });
            } catch (error) {
                console.log('gai :: subtask_rating_model :: error :: ', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Something went wrong',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    rateSubtask(query) {
        return new Promise(async (resolve, reject) => {
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_addr'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            const serviceRequestId = requester['srvc_req_id'];
            const subtaskId = requester['sbtsk_id'];

            if (!serviceRequestId || !subtaskId) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Bad Request',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }

            const subtaskDetailsDbRes = await dbObj.tms_get_subtasks_details(
                2,
                null,
                null,
                null,
                1,
                serviceRequestId,
                subtaskId
            );
            if (subtaskDetailsDbRes[0].tms_get_subtasks_details?.data) {
                allQueues.WIFY_GAI_RATING_FOR_TECHNICIAN_SUBTASK.addJob(
                    subtaskDetailsDbRes[0].tms_get_subtasks_details?.data
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Something went wrong',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            } else {
                console.log(
                    `No subtask found with given ${serviceRequestId} service request id and ${subtaskId} subtask id`
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        `No subtask found with given ${serviceRequestId} service request id and ${subtaskId} subtask id`,
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    async updateSubtaskGaiRatingData(query) {
        const {
            order_id,
            relativeGaiRating,
            absoluteGaiRating,
            sbtsk_db_id,
            org_id,
            srvc_req_db_id,
            sbtsk_type_id,
        } = query;
        if (!this.db) {
            return null;
        }
        const dbObj = this.db;
        if (!dbObj) {
            console.log(
                'gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: db not found'
            );
        }

        try {
            const _userData = {
                ip_address: this.ip_address,
                user_agent: this.user_agent_,
                org_id: org_id,
            };
            // console.log('gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: _userData :: ', _userData);
            const userFormData = JSON.stringify(_userData);
            console.log(
                'gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: userFormData :: ',
                userFormData
            );
            const userRes =
                await dbObj.tms_get_or_create_system_usr(userFormData);
            const user_id =
                userRes[0]?.tms_get_or_create_system_usr?.data?.usr_id;
            console.log(
                'gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: user_id :: ',
                user_id,
                userRes
            );
            let _subtaskData = {
                org_id,
                usr_id: user_id,
                ip_address: this.ip_address || '',
                user_agent: this.user_agent_ || '',
                srvc_req_id: srvc_req_db_id,
                sbtsk_type_id,
            };

            if (absoluteGaiRating) {
                _subtaskData.gai_rating = { gemini: absoluteGaiRating };
            }
            if (relativeGaiRating) {
                _subtaskData.relative_gai_rating = {
                    gemini: relativeGaiRating,
                };
            }
            const subtaskFormData = JSON.stringify(_subtaskData);
            console.log(
                'gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: subtaskFormData :: ',
                subtaskFormData
            );
            console.log(
                'gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: sbtsk_db_id :: ',
                sbtsk_db_id
            );
            // let response = await dbObj.tms_create_update_subtask_gai(gaiRating, sbtsk_db_id)
            const subtaskUpdate = await dbObj.tms_create_subtask(
                subtaskFormData,
                sbtsk_db_id
            );
            // console.log('gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: subtaskUpdate :: ', subtaskUpdate);
            // console.log('subtaskUpdate :: ', subtaskUpdate[ 0 ].tms_create_subtask);
            return subtaskUpdate[0].tms_create_subtask;
        } catch (error) {
            console.log(
                'gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: error :: ',
                error
            );
            return null;
        }
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance() {
        const instance = new subtask_rating_model();
        return instance;
    }

    getSubtaskRatingModelData(
        subtask_rating_model,
        getUserAndOrgIdFrQuery = false
    ) {
        let userAndOrgIdFrQuery = {};
        if (getUserAndOrgIdFrQuery) {
            userAndOrgIdFrQuery = {
                org_id: subtask_rating_model.getOrgId(this.userContext),
                usr_id: subtask_rating_model.getUUID(this.userContext),
            };
        }
        return {
            ip_address: subtask_rating_model.ip_address,
            user_agent: subtask_rating_model.user_agent_,
            user_context: subtask_rating_model.user_context,
            srvc_type_id: subtask_rating_model.srvc_type_id,
            srvc_req_id: subtask_rating_model.srvc_req_id,
            ...userAndOrgIdFrQuery,
        };
    }

    // getFreshInstance(model) {
    //   const clonedInstance = new subtask_rating_model();
    //   Object.assign(clonedInstance, model);
    //   return clonedInstance;
    // }
}

module.exports = new subtask_rating_model();
